-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS educational_institution CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE educational_institution;

-- جدول الأساتذة (teachers)
CREATE TABLE teachers (
    teacher_id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    gender VARCHAR(10),
    contact_info VARCHAR(200),
    hire_date DATE,
    subject_specialization VARCHAR(100),
    salary DECIMAL(10, 2),
    hourly_rate DECIMAL(10, 2)
);

-- جدول التلاميذ (students)
CREATE TABLE students (
    student_id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    gender VARCHAR(10),
    contact_info VARCHAR(200),
    grade_level VARCHAR(50),
    enrollment_date DATE
);

-- جدول الأقسام (departments)
CREATE TABLE departments (
    department_id INT AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL
);

-- جدول المواد (subjects)
CREATE TABLE subjects (
    subject_id INT AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL,
    department_id INT,
    FOREIGN KEY (department_id) REFERENCES departments(department_id)
);

-- جدول الأفواج (groups)
CREATE TABLE `groups` (
    group_id INT AUTO_INCREMENT PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL,
    department_id INT,
    FOREIGN KEY (department_id) REFERENCES departments(department_id)
);

-- جدول الحصص (lessons)
CREATE TABLE lessons (
    lesson_id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT,
    department_id INT,
    subject_id INT,
    lesson_time TIME,
    lesson_duration INT,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
    FOREIGN KEY (department_id) REFERENCES departments(department_id),
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id)
);

-- جدول الفصول (classes)
CREATE TABLE classes (
    class_id INT AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(100) NOT NULL,
    group_id INT,
    FOREIGN KEY (group_id) REFERENCES `groups`(group_id)
);

-- جدول العلاقة بين الطلاب والفصول
CREATE TABLE student_classes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT,
    class_id INT,
    FOREIGN KEY (student_id) REFERENCES students(student_id),
    FOREIGN KEY (class_id) REFERENCES classes(class_id)
);

-- جدول الأوقات (schedule)
CREATE TABLE schedule (
    schedule_id INT AUTO_INCREMENT PRIMARY KEY,
    lesson_id INT,
    schedule_time DATETIME,
    break_time BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (lesson_id) REFERENCES lessons(lesson_id)
);

-- جدول العطل (holidays)
CREATE TABLE holidays (
    holiday_id INT AUTO_INCREMENT PRIMARY KEY,
    holiday_name VARCHAR(100) NOT NULL,
    holiday_date DATE NOT NULL
);

-- جدول الأجور الخاصة بالحصة (hourly_wages)
CREATE TABLE hourly_wages (
    wage_id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT,
    lesson_id INT,
    wage_rate DECIMAL(10, 2) NOT NULL,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
    FOREIGN KEY (lesson_id) REFERENCES lessons(lesson_id)
);

-- جدول الأجور الوظيفية (salaries)
CREATE TABLE salaries (
    salary_id INT AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT,
    base_salary DECIMAL(10, 2) NOT NULL,
    bonus DECIMAL(10, 2) DEFAULT 0,
    total_salary DECIMAL(10, 2) GENERATED ALWAYS AS (base_salary + bonus) STORED,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
);

-- جدول التقييمات (feedback)
CREATE TABLE feedback (
    feedback_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT,
    teacher_id INT,
    subject_id INT,
    feedback_text TEXT,
    rating INT CHECK (rating BETWEEN 1 AND 5),
    feedback_date DATE DEFAULT CURRENT_DATE,
    FOREIGN KEY (student_id) REFERENCES students(student_id),
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id)
);

-- إضافة فهارس لتحسين الأداء
CREATE INDEX idx_teacher_subject ON teachers(subject_specialization);
CREATE INDEX idx_student_grade ON students(grade_level);
CREATE INDEX idx_lesson_teacher ON lessons(teacher_id);
CREATE INDEX idx_schedule_time ON schedule(schedule_time);